<?php

namespace Drupal\scraper_news\Form;

use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;
use Drupal\node\Entity\Node;
use Drupal\media\Entity\Media;

class ScraperCareerForm extends FormBase
{

  private const CARRIER_URLS = [
    'fr' => 'https://www.transport.gov.ma/Gouvernance/Formation/Recrutement/Pages/Concours-de-recrutement.aspx',
    'ar' => 'https://www.transport.gov.ma/AR/gouvernance/Formation/Recrutement/Pages/Recrutements.aspx',
  ];


  public function getFormId()
  {
    return 'scraper_career_form';
  }

  public function buildForm(array $form, FormStateInterface $form_state)
  {
    $form['language'] = [
      '#type' => 'select',
      '#title' => $this->t('Langue'),
      '#options' => [
        'fr' => $this->t('Français'),
        'ar' => $this->t('Arabe'),
      ],
      '#required' => TRUE,
      '#default_value' => 'fr',
    ];

    $form['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Scraper les carrières'),
    ];
    return $form;
  }

  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    $language = $form_state->getValue('language');
    $batch = [
      'title' => $this->t('Scraping des carrieres en cours...'),
      'operations' => [
        [[static::class, 'processCarrierPage'], [$language]],
      ],
      'finished' => [static::class, 'batchFinished'],
      'error_message' => $this->t('Une erreur est survenue lors du traitement.'),
      'init_message' => $this->t('Préparation du traitement...'),
    ];
    \Drupal::service('batch.builder')
      ->setTitle($batch['title'])
      ->setInitMessage($batch['init_message'])
      ->setErrorMessage($batch['error_message'])
      ->setFinishCallback($batch['finished'])
      ->addOperation($batch['operations'][0][0], $batch['operations'][0][1])
      ->toArray();
  }

  public static function processCarrierPage($language, &$context)
  {
    $url = self::CARRIER_URLS[$language];
    $html = self::fetchHtml($url);
    $events = self::parseCarriéreHtml($html);

    // Filter events to only include 2024 and below
    $filteredEvents = self::filterEventsByYear($events, 2024);

    $context['message'] = t('Processing @count career events...', ['@count' => count($filteredEvents)]);

    foreach ($filteredEvents as $event) {
      try {
        self::CreateCareerNode($event, $language);
        \Drupal::logger('scraper_news')->notice('Career node created: @title', [
          '@title' => $event['title'],
        ]);
      } catch (\Exception $e) {
        \Drupal::logger('scraper_news')->error('Error creating career node: @message', [
          '@message' => $e->getMessage(),
        ]);
      }
    }

    $context['results']['processed'] = count($filteredEvents);
    $context['results']['language'] = $language;
  }

  private static function fetchHtml($url)
  {
    $ch = curl_init();
    if ($ch === false) {
      throw new \Exception('Failed to initialize cURL');
    }

    curl_setopt_array($ch, [
      CURLOPT_URL => $url,
      CURLOPT_RETURNTRANSFER => true,
      CURLOPT_FOLLOWLOCATION => true,
      CURLOPT_TIMEOUT => 30,
      CURLOPT_SSL_VERIFYPEER => false,
      CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    ]);

    $html = curl_exec($ch);

    if ($html === false) {
      $error = curl_error($ch);
      curl_close($ch);
      throw new \Exception("cURL error: $error");
    }

    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
      throw new \Exception("HTTP request failed with status $httpCode");
    }

    return $html;
  }

  private static function parseCarriéreHtml($html)
  {
    $dom = new \DOMDocument();
    @$dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'), LIBXML_NOERROR);
    $xpath = new \DOMXPath($dom);

    $events = [];

    // Chercher le div principal contenant les données
    $mainDiv = $xpath->query('//div[contains(@id, "ctl00_PlaceHolderMain_ctl00__ControlWrapper_RichHtmlField")]')->item(0);

    if (!$mainDiv) {
      return $events;
    }

    // Récupérer tous les divs qui contiennent des dates (format dd/mm/yyyy)
    $dateDivs = $xpath->query('.//div[contains(., "/") and contains(., ":")]', $mainDiv);

    foreach ($dateDivs as $dateDiv) {
      $textContent = trim($dateDiv->textContent);

      // Ignorer les lignes avec des astérisques ou vides
      if (empty($textContent) || strpos($textContent, '*') !== false) {
        continue;
      }

      // Extraire la date au format dd/mm/yyyy
      if (preg_match('/(\d{2}\/\d{2}\/\d{4})/', $textContent, $dateMatches)) {
        $date = $dateMatches[1];

        // Chercher le lien PDF dans ce div
        $linkElement = $xpath->query('.//a[@href]', $dateDiv)->item(0);

        if ($linkElement) {
          $href = $linkElement->getAttribute('href');
          $linkText = trim($linkElement->textContent);

          // Nettoyer le texte du lien (enlever les extensions d'image, etc.)
          $linkText = preg_replace('/\.(pdf|PDF)$/', '', $linkText);
          $linkText = trim($linkText);

          // Construire l'URL complète si nécessaire
          $fileUrl = $href;
          if (strpos($href, 'http') !== 0) {
            // Ajouter le domaine de base si l'URL est relative
            $fileUrl = 'https://www.transport.gov.ma' . $href;
          }

          $events[] = [
            'date' => $date,
            'title' => $linkText,
            'file_url' => $fileUrl,
            'file_name' => basename($href),
            'raw_text' => $textContent
          ];
        }
      }
    }

    return $events;
  }

  private static function filterEventsByYear($events, $maxYear)
  {
    $filteredEvents = [];

    foreach ($events as $event) {
      if (empty($event['date'])) {
        continue;
      }

      // Parse the date (format: dd/mm/yyyy)
      $date = \DateTime::createFromFormat('d/m/Y', $event['date']);
      if (!$date) {
        continue;
      }

      $year = (int) $date->format('Y');
      if ($year <= $maxYear) {
        $filteredEvents[] = $event;
      }
    }

    return $filteredEvents;
  }

  public static function CreateCareerNode($event, $language)
  {
    try {
      // Validation des données
      if (empty($event['title']) || empty($event['date'])) {
        throw new \Exception('Données manquantes pour la création du nœud');
      }

      // Conversion de la date
      $date = \DateTime::createFromFormat('d/m/Y', $event['date']);
      if (!$date) {
        throw new \Exception('Format de date invalide: ' . $event['date']);
      }

      // Check if node already exists
      $existingNodes = \Drupal::entityTypeManager()
        ->getStorage('node')
        ->loadByProperties([
          'type' => 'actualite',
          'title' => $event['title'],
          'langcode' => $language,
        ]);

      if (!empty($existingNodes)) {
        \Drupal::logger('scraper_news')->notice('Node already exists: @title', [
          '@title' => $event['title'],
        ]);
        return;
      }

      $file = null;
      $media = null;

      // Download and create file if URL exists
      if (!empty($event['file_url'])) {
        $file = self::downloadAndCreateFile($event['file_url'], $event['file_name']);
        if ($file) {
          // Create media entity for the document
          $media = Media::create([
            'bundle' => 'document',
            'name' => $event['file_name'],
            'field_media_document' => [
              'target_id' => $file->id(),
              'description' => $event['file_name'],
            ],
            'status' => 1,
            'uid' => 1,
          ]);
          $media->save();
        }
      }

      // Création du nœud avec le type actualite (car carriere n'existe pas)
      $nodeData = [
        'type' => 'actualite',
        'title' => $event['title'],
        'field_date' => [
          'value' => $date->format('Y-m-d'),
        ],
        'field_type_de_contenu' => 'Carrière',
        'status' => 1,
        'langcode' => $language,
        'uid' => 1,
      ];

      // Add media reference if available
      if ($media) {
        // Note: You may need to add a media reference field to actualite content type
        // For now, we'll add it to the body field as a link
        $nodeData['body'] = [
          'value' => '<p>Document: <a href="' . $event['file_url'] . '" target="_blank">' . $event['file_name'] . '</a></p>',
          'format' => 'basic_html',
        ];
      }

      $node = Node::create($nodeData);
      $node->save();

      \Drupal::logger('scraper_news')->notice('Career node created: @title (ID: @nid)', [
        '@title' => $event['title'],
        '@nid' => $node->id(),
      ]);

    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error creating career node: @message', [
        '@message' => $e->getMessage(),
      ]);
      throw $e;
    }
  }

  private static function downloadAndCreateFile($url, $filename)
  {
    try {
      // Create a unique filename to avoid conflicts
      $fileSystem = \Drupal::service('file_system');
      $directory = 'public://career-documents';

      // Ensure directory exists
      $fileSystem->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY);

      // Generate unique filename
      $destination = "$directory/$filename";

      // Download the file
      $data = file_get_contents($url);
      if ($data === FALSE) {
        throw new \Exception("Failed to download file from: $url");
      }

      // Save the file
      $uri = $fileSystem->saveData($data, $destination);
      if (!$uri) {
        throw new \Exception("Failed to save file: $filename");
      }

      // Create file entity
      $file = File::create([
        'filename' => basename($uri),
        'uri' => $uri,
        'status' => 1,
        'uid' => 1,
      ]);
      $file->save();

      return $file;

    } catch (\Exception $e) {
      \Drupal::logger('scraper_news')->error('Error downloading file @url: @message', [
        '@url' => $url,
        '@message' => $e->getMessage(),
      ]);
      return null;
    }
  }

  public static function batchFinished($success, $results, $operations = [])
  {
    if ($success) {
      $processed = $results['processed'] ?? 0;
      $language = $results['language'] ?? 'unknown';

      \Drupal::messenger()->addMessage(
        t('Successfully processed @count career items in @language.', [
          '@count' => $processed,
          '@language' => $language,
        ])
      );
    } else {
      \Drupal::messenger()->addError(t('An error occurred during the batch processing.'));
    }
  }
}
